<!DOCTYPE html>

<html lang="en">

<head>

  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CasaMia-Modern Sofa</title>
  <link rel="stylesheet" href="../style.css">
</head>

<body>

  <nav style="display: flex; align-items: center; gap: 20px;">
    <p class="logo">
      CasaMia
    </p>
    <a class="pages-tabs" href="../home.html">Home</a>
    <a class="pages-tabs" href="../shop.html">Shop</a>
    <a class="pages-tabs active" href="../categories.html">Categories</a>
    <a class="pages-tabs" href="../about.html">About</a>
    <a class="pages-tabs" href="../contact.html">Contact</a>
  </nav>

  <section class="product-page">
    <div class="section-header">
      <a href="index.html" class="back-link">← Back to Living Room</a>
      <p class="section-title">Product Details</p>
    </div>

    <div class="product-container">
      <!-- Left Column - Images -->
      <div class="product-images">
        <div class="main-image">
          <img src="../Images/livingRoom/MS01.jpg" alt="Modern Sectional Sofa" class="clickable-image">
        </div>
        <div class="thumbnail-images">
          <img src="../Images/livingRoom/MS01.jpg" alt="Modern Sectional Sofa - View 1" class="thumbnail clickable-image">
          <img src="../Images/livingRoom/MS01.jpg" alt="Modern Sectional Sofa - View 2" class="thumbnail clickable-image">
          <img src="../Images/livingRoom/MS01.jpg" alt="Modern Sectional Sofa - View 3" class="thumbnail clickable-image">
          <img src="../Images/livingRoom/MS01.jpg" alt="Modern Sectional Sofa - View 4" class="thumbnail clickable-image">
        </div>
      </div>

      <!-- Right Column - Product Details -->
      <div class="product-details">
        <h1 class="product-title">Modern Sectional Sofa</h1>
        <p class="product-price">$1,899</p>

        <div class="product-description">
          <p>Comfortable 3-seater sectional sofa with premium fabric upholstery.</p>
        </div>

        <div class="product-features">
          <h3>Features:</h3>
          <ul>
            <li>Premium fabric</li>
            <li>3-seater</li>
            <li>Modern design</li>
            <li>Easy assembly</li>
          </ul>
        </div>

        <div class="product-actions">
          <div class="quantity-section">
            <label for="quantity">Quantity:</label>
            <select id="quantity" name="quantity">
              <option value="1" selected>1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
            </select>
          </div>

          <div class="action-buttons">
            <button class="add-to-cart-btn">Add to Cart</button>
            <button class="add-to-wishlist-btn">Add to Wishlist</button>
          </div>
        </div>

        <div class="product-footer">
          <p>✓ Free shipping on orders over $500</p>
          <p>✓ 30-day return policy</p>
          <p>✓ 2-year warranty included</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Image Modal -->
  <div id="imageModal" class="modal-overlay">
    <div class="modal-content">
      <button class="modal-close" onclick="closeModal()">&times;</button>
      <img id="modalImage" class="modal-image" src="" alt="">
    </div>
  </div>

  <script>
    // Modal functionality
    function openModal(imageSrc, imageAlt) {
      const modal = document.getElementById('imageModal');
      const modalImage = document.getElementById('modalImage');

      modalImage.src = imageSrc;
      modalImage.alt = imageAlt;
      modal.classList.add('active');
      document.body.style.overflow = 'hidden';
    }

    function closeModal() {
      const modal = document.getElementById('imageModal');
      modal.classList.remove('active');
      document.body.style.overflow = 'auto';
    }

    // Thumbnail functionality
    function switchMainImage(thumbnailSrc) {
      const mainImage = document.querySelector('.main-image img');
      mainImage.src = thumbnailSrc;
    }

    document.addEventListener('DOMContentLoaded', function() {
      // Add click listeners to clickable images
      const clickableImages = document.querySelectorAll('.clickable-image');
      clickableImages.forEach(function(image) {
        image.addEventListener('click', function() {
          openModal(this.src, this.alt);
        });
      });

      // Add click listeners to thumbnails
      const thumbnails = document.querySelectorAll('.thumbnail');
      thumbnails.forEach(function(thumbnail) {
        thumbnail.addEventListener('click', function() {
          switchMainImage(this.src);
          // Remove active class from all thumbnails
          thumbnails.forEach(t => t.classList.remove('active'));
          // Add active class to clicked thumbnail
          this.classList.add('active');
        });
      });

      // Set first thumbnail as active
      if (thumbnails.length > 0) {
        thumbnails[0].classList.add('active');
      }

      // Modal close events
      document.getElementById('imageModal').addEventListener('click', function(e) {
        if (e.target === this) closeModal();
      });

      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') closeModal();
      });
    });
  </script>

  <footer class="footer">
    <div class="footer-content">
      <div class="footer-column">
        <h4>CasaMia</h4>
        <p>Transform your space with our curated collection of premium furniture and home décor.</p>
      </div>

      <div class="footer-column">
        <h4>Quick Links</h4>
        <a href="../home.html">Home</a>
        <a href="../shop.html">Shop</a>
        <a href="../categories.html">Categories</a>
        <a href="../about.html">About</a>
        <a href="../contact.html">Contact</a>
      </div>

      <div class="footer-column">
        <h4>Customer Service</h4>
        <p>Shipping Info</p>
        <p>Returns</p>
        <p>Size Guide</p>
        <p>Contact</p>
      </div>

      <div class="footer-column">
        <h4>Follow Us</h4>
        <div class="footer-social">
          <a href="https://www.facebook.com/"><img src="../Images/fb.jpg" alt="facebook logo"></a>
          <a href="https://www.twitter.com/"><img src="../Images/x.jpg" alt="twitter logo"></a>
          <a href="https://www.instagram.com/"><img src="../Images/instagram.jpg" alt="instagram logo"></a>
        </div>
      </div>
    </div>
    <p class="footer-copyright">© 2025 CasaMia. All rights reserved.</p>
  </footer>

</body>

</html>