<!DOCTYPE html>

<html lang="en">

<head>

  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CasaMia-Living Room Products</title>
  <link rel="stylesheet" href="../style.css">
</head>

<body>

  <nav style="display: flex; align-items: center; gap: 20px;">
  <p class="logo">
    CasaMia
  </p>
  <a class="pages-tabs" href="../home.html">Home</a>
  <a class="pages-tabs" href="../shop.html">Shop</a>
  <a class="pages-tabs active" href="../categories.html">Categories</a>
  <a class="pages-tabs" href="../about.html">About</a>
  <a class="pages-tabs" href="../contact.html">Contact</a>
</nav>

  <section class="products-section">
    <div class="section-header">
      <a href="../categories.html" class="back-link"><- Back to Categories</a>
      <p class="section-title">Living Room Products</p>
    </div>

    <div class="products-grid">
      <figure>
        <img src="../Images/livingRoom/MS01.jpg" alt="modern sofa sample" height="300" width="300" class="clickable-image">
        <figcaption>Modern Sofa</figcaption>
        <a href="product01.html" class="product-link">View Product →</a>
      </figure>

      <figure>
        <img src="../Images/livingRoom/MS01.jpg" alt="modern sofa sample" height="300" width="300" class="clickable-image">
        <figcaption>Modern Sofa</figcaption>
        <a href="product02.html" class="product-link">View Product →</a>
      </figure>

      <figure>
        <img src="../Images/livingRoom/MS01.jpg" alt="modern sofa sample" height="300" width="300" class="clickable-image">
        <figcaption>Modern Sofa</figcaption>
        <a href="product03.html" class="product-link">View Product →</a>
      </figure>

      <figure>
        <img src="../Images/livingRoom/MS01.jpg" alt="modern sofa sample" height="300" width="300" class="clickable-image">
        <figcaption>Modern Sofa</figcaption>
        <a href="product04.html" class="product-link">View Product →</a>
      </figure>

      <figure>
        <img src="../Images/livingRoom/MS01.jpg" alt="modern sofa sample" height="300" width="300" class="clickable-image">
        <figcaption>Modern Sofa</figcaption>
        <a href="product05.html" class="product-link">View Product →</a>
      </figure>

      <figure>
        <img src="../Images/livingRoom/MS01.jpg" alt="modern sofa sample" height="300" width="300" class="clickable-image">
        <figcaption>Modern Sofa</figcaption>
        <a href="product06.html" class="product-link">View Product →</a>
      </figure>
    </div>
  </section>

  <!-- Image Modal -->
  <div id="imageModal" class="modal-overlay">
    <div class="modal-content">
      <button class="modal-close" onclick="closeModal()">&times;</button>
      <img id="modalImage" class="modal-image" src="" alt="">
    </div>
  </div>

  <script>
    // Function to open modal with clicked image
    function openModal(imageSrc, imageAlt) {
      const modal = document.getElementById('imageModal');
      const modalImage = document.getElementById('modalImage');

      modalImage.src = imageSrc;
      modalImage.alt = imageAlt;
      modal.classList.add('active');

      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden';
    }

    // Function to close modal
    function closeModal() {
      const modal = document.getElementById('imageModal');
      modal.classList.remove('active');

      // Restore body scrolling
      document.body.style.overflow = 'auto';
    }

    // Add click event listeners to all clickable images
    document.addEventListener('DOMContentLoaded', function() {
      const clickableImages = document.querySelectorAll('.clickable-image');

      clickableImages.forEach(function(image) {
        image.addEventListener('click', function() {
          openModal(this.src, this.alt);
        });
      });

      // Close modal when clicking outside the image
      document.getElementById('imageModal').addEventListener('click', function(e) {
        if (e.target === this) {
          closeModal();
        }
      });

      // Close modal with Escape key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          closeModal();
        }
      });
    });
  </script>

  <footer class="footer">
    <div class="footer-content">
      <div class="footer-column">
        <h4>CasaMia</h4>
        <p>Transform your space with our curated collection of premium furniture and home décor.</p>
      </div>

      <div class="footer-column">
        <h4>Quick Links</h4>
        <a href="../home.html">Home</a>
        <a href="../shop.html">Shop</a>
        <a href="../categories.html">Categories</a>
        <a href="../about.html">About</a>
        <a href="../contact.html">Contact</a>
      </div>

      <div class="footer-column">
        <h4>Customer Service</h4>
        <p>Shipping Info</p>
        <p>Returns</p>
        <p>Size Guide</p>
        <p>Contact</p>
      </div>

      <div class="footer-column">
        <h4>Follow Us</h4>
        <div class="footer-social">
          <a href="https://www.facebook.com/" target="_blank"><img src="../Images/fb.jpg" alt="facebook logo"></a>
          <a href="https://www.twitter.com/" target="_blank"><img src="../Images/x.jpg" alt="twitter logo"></a>
          <a href="https://www.instagram.com/" target="_blank"><img src="../Images/instagram.jpg" alt="instagram logo"></a>
        </div>
      </div>
    </div>
    <p class="footer-copyright">© 2025 CasaMia. All rights reserved.</p>
  </footer>

</body>

</html>
