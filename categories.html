<!DOCTYPE html>
<!--
  CasaMia Furniture Website - Categories Page

  PURPOSE:
  - Display all product categories in an organized grid
  - Each category is a clickable card leading to product listings
  - Pure HTML/CSS implementation with hover effects

  FEATURES:
  - 2x2 grid layout for 4 main categories
  - Clickable category cards with image, title, description, and action text
  - Hover effects for better user interaction
  - Responsive design for mobile devices

  NAVIGATION STRUCTURE:
  categories.html → [category]Products/index.html → product01.html (etc.)
-->

<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CasaMia - Categories</title>
  <link rel="stylesheet" href="style.css">
</head>

<body>

<!-- Navigation bar - Categories page is marked as active -->
<nav style="display: flex; align-items: center; gap: 20px;">
  <p class="logo">
    CasaMia
  </p>
  <a class="pages-tabs" href="home.html">Home</a>
  <a class="pages-tabs" href="shop.html">Shop</a>
  <a class="pages-tabs active" href="categories.html">Categories</a>
  <a class="pages-tabs" href="about.html">About</a>
  <a class="pages-tabs" href="contact.html">Contact</a>
</nav>

<!--
  CATEGORIES SECTION
  - Main content area displaying all product categories
  - Each category is a fully clickable card
  - Cards include: image, category name, product description, action text
  - Links lead to dedicated product listing pages
-->
<section class="categories-section">
  <p>Shop by Category</p>

  <!-- 2x2 grid container for category cards -->
  <div class="categories-grid">

    <!-- LIVING ROOM CATEGORY CARD -->
    <figure>
      <!-- Entire card is clickable - leads to living room products -->
      <a href="livingRoomProducts/index.html" class="category-link">
        <img src="Images/livingRoom/S01.jpg" alt="living room" height="300" width="300">
        <p>Living Room</p> <!-- Category title -->
        <p>Sofas, Sofa Beds, Chairs, Side Tables and Pouffs</p> <!-- Product description -->
        <p>Shop Living Room →</p> <!-- Call-to-action text -->
      </a>
    </figure>

    <!-- STORAGE UNIT CATEGORY CARD -->
    <figure>
      <a href="storageUnitProducts/index.html" class="category-link">
        <img src="Images/storageUnit/SU02.jpg" alt="storage unit" height="300" width="300">
        <p>Storage Unit</p>
        <p>Shoe Cabinets, Coffee Corners and Drawer Units</p>
        <p>Shop Storage Unit →</p>
      </a>
    </figure>

    <!-- OUTDOOR FURNITURE CATEGORY CARD -->
    <figure>
      <a href="outdoorFurnitureProducts/index.html" class="category-link">
        <img src="Images/outdoorFurniture/OF02.jpg" alt="outdoor furniture" height="300" width="300">
        <p>Outdoor Furniture</p>
        <p>Bean Bags and Garden Furniture</p>
        <p>Shop Outdoor Furniture →</p>
      </a>
    </figure>

    <!-- LIGHTING CATEGORY CARD -->
    <figure>
      <a href="lightingProducts/index.html" class="category-link">
        <img src="Images/lighting/L02.jpg" alt="lighting" height="300" width="300">
        <p>Lighting</p>
        <p>Pendants, Table Lamps, Floor Lamps and Wall Lights</p>
        <p>Shop Lighting →</p>
      </a>
    </figure>

  </div>
</section>

  <footer class="footer">
    <div class="footer-content">
      <div class="footer-column">
        <h4>CasaMia</h4>
        <p>Transform your space with our curated collection of premium furniture and home décor.</p>
      </div>

      <div class="footer-column">
        <h4>Quick Links</h4>
        <a href="home.html">Home</a>
        <a href="shop.html">Shop</a>
        <a href="categories.html">Categories</a>
        <a href="about.html">About</a>
        <a href="contact.html">Contact</a>
      </div>

      <div class="footer-column">
        <h4>Customer Service</h4>
        <p>Shipping Info</p>
        <p>Returns</p>
        <p>Size Guide</p>
        <p>Contact</p>
      </div>

      <div class="footer-column">
        <h4>Follow Us</h4>
        <div class="footer-social">
          <a href="https://www.facebook.com/" target="_blank"><img src="Images/fb.jpg" alt="facebook logo"></a>
          <a href="https://www.twitter.com/" target="_blank"><img src="Images/x.jpg" alt="twitter logo"></a>
          <a href="https://www.instagram.com/" target="_blank"><img src="Images/instagram.jpg" alt="instagram logo"></a>
        </div>
      </div>
    </div>
    <p class="footer-copyright">© 2025 CasaMia. All rights reserved.</p>
  </footer>

</body>

</html>
