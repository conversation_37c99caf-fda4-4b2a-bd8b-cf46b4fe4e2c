<!DOCTYPE html>
<!--
  CasaMia Furniture Website - Home Page

  TECHNOLOGY STACK:
  - Pure HTML5 and CSS3 (No JavaScript dependencies)
  - Responsive design using CSS Grid and Flexbox
  - CSS-only image modals using :target pseudo-selector

  DEVELOPER NOTES:
  - All styling is contained in style.css
  - Images are stored in /Images/ directory with organized subfolders
  - Navigation uses .pages-tabs class with .active state management
  - Footer is consistent across all pages

  MAINTENANCE:
  - To add new products: Add figure elements to .products-grid
  - To modify hero section: Update .hero-section content
  - To change navigation: Update nav links and ensure .active class is on current page
-->

<html lang="en">

<head>
  <!-- Basic HTML5 meta tags for responsive design -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CasaMia - Premium Furniture</title>

  <!-- Single CSS file contains all styling - no external dependencies -->
  <link rel="stylesheet" href="style.css">
</head>

<body>

<!--
  NAVIGATION BAR
  - Uses flexbox for horizontal layout
  - .logo class for brand styling
  - .pages-tabs for navigation links
  - .active class indicates current page
  - Inline style used for nav container (consider moving to CSS)
-->
<nav style="display: flex; align-items: center; gap: 20px;">
  <p class="logo">
    CasaMia
  </p>
  <!-- Navigation links - .active class shows current page -->
  <a class="pages-tabs active" href="home.html">Home</a>
  <a class="pages-tabs" href="shop.html">Shop</a>
  <a class="pages-tabs" href="categories.html">Categories</a>
  <a class="pages-tabs" href="about.html">About</a>
  <a class="pages-tabs" href="contact.html">Contact</a>
</nav>

<!--
  HERO SECTION
  - Full-width banner with background image (set in CSS)
  - Contains main headline, subtitle, and call-to-action button
  - Background image: images/home.jpg
  - Uses .hero-section class for styling
-->
<section class="hero-section">
  <!-- Main headline - large, prominent text -->
  <p>Transform Your Space</p>

  <!-- Subtitle - descriptive text about the brand -->
  <p>Discover Our Premium Furniture Collection for Your Home at CasaMia</p>

  <!-- Call-to-action button linking to shop page -->
  <a class="hero-button" href="shop.html"><button>Shop Collection</button></a>
</section>

<!--
  FEATURED CATEGORIES SECTION
  - Displays 4 main product categories in a 2x2 grid
  - Each category shows representative image with caption
  - Grid layout handled by .categories-grid class
  - Images stored in organized subfolders under /Images/
-->
<section class="categories-section">
  <!-- Section title -->
  <p>Featured Categories</p>

  <!-- 2x2 grid container for category items -->
  <div class="categories-grid">
    <!-- Living Room Category -->
    <figure>
      <img src="Images/livingRoom/L01.jpg" alt="living room sample" height="300" width="300">
      <figcaption>Living Room</figcaption>
    </figure>

    <!-- Storage Unit Category -->
    <figure>
      <img src="Images/storageUnit/su01.jpeg" alt="storage unit sample" height="300" width="300">
      <figcaption>Storage Unit</figcaption>
    </figure>

    <!-- Outdoor Furniture Category -->
    <figure>
      <img src="Images/outdoorFurniture/of01.jpg" alt="outdoor furniture sample" height="300" width="300">
      <figcaption>Outdoor Furniture</figcaption>
    </figure>

    <!-- Lighting Category -->
    <figure>
      <img src="Images/lighting/l01.jpg" alt="lighting sample" height="300" width="300">
      <figcaption>Lighting</figcaption>
    </figure>
  </div>
</section>

<!--
  FEATURED PRODUCTS SECTION
  - Showcases 3 popular products from different categories
  - Uses same grid system as categories but with 3 items
  - Products can be updated by changing figure elements
-->
<section class="products-section">
  <!-- Section title -->
  <p>Featured Products</p>

  <!-- Product grid container -->
  <div class="products-grid">
    <!-- Modern Sofa Product -->
    <figure>
      <img src="Images/livingRoom/MS01.jpg" alt="modern sofa sample" height="300" width="300">
      <figcaption>Modern Sofa</figcaption>
    </figure>

    <!-- Coffee Corner Product -->
    <figure>
      <img src="Images/storageUnit/CC01.jpg" alt="coffee corner sample" height="300" width="300">
      <figcaption>Coffee Corners</figcaption>
    </figure>

    <!-- Floor Lamp Product -->
    <figure>
      <img src="Images/lighting/FL01.jpg" alt="floor lamps sample" height="300" width="300">
      <figcaption>Floor Lamp</figcaption>
    </figure>
  </div>
</section>

<!--
  FOOTER SECTION
  - Consistent across all pages
  - 4-column layout: Brand info, Quick links, Customer service, Social media
  - Responsive design: stacks on mobile devices
  - Social media links open in new tabs (target="_blank")
  - Copyright notice at bottom
-->
<footer class="footer">
  <!-- Main footer content container -->
  <div class="footer-content">
    <!-- Brand Information Column -->
    <div class="footer-column">
      <h4>CasaMia</h4>
      <p>Transform your space with our curated collection of premium furniture and home décor.</p>
    </div>

    <!-- Quick Navigation Links Column -->
    <div class="footer-column">
      <h4>Quick Links</h4>
      <a href="home.html">Home</a>
      <a href="shop.html">Shop</a>
      <a href="categories.html">Categories</a>
      <a href="about.html">About</a>
      <a href="contact.html">Contact</a>
    </div>

    <!-- Customer Service Information Column -->
    <div class="footer-column">
      <h4>Customer Service</h4>
      <p>Shipping Info</p>
      <p>Returns</p>
      <p>Size Guide</p>
      <p>Contact</p>
    </div>

    <!-- Social Media Links Column -->
    <div class="footer-column">
      <h4>Follow Us</h4>
      <div class="footer-social">
        <!-- Social media icons with external links -->
        <a href="https://www.facebook.com/" target="_blank"><img src="Images/fb.jpg" alt="facebook logo"></a>
        <a href="https://www.twitter.com/" target="_blank"><img src="Images/x.jpg" alt="twitter logo"></a>
        <a href="https://www.instagram.com/" target="_blank"><img src="Images/instagram.jpg" alt="instagram logo"></a>
      </div>
    </div>
  </div>

  <!-- Copyright notice -->
  <p class="footer-copyright">© 2025 CasaMia. All rights reserved.</p>
</footer>

</body>
</html>
