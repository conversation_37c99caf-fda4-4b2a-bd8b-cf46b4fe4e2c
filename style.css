/* ========================================
   NAVIGATION STYLES
   ======================================== */

/* Logo styling in navigation */
.logo {
  color: black;
  font-size: 24px;
  font-weight: normal;
  font-family: sans-serif;
  margin-right: 60px;
}

/* Navigation tab links */
.pages-tabs {
  color: black;
  font-size: 16px;
  font-family: sans-serif;
  font-weight: lighter;
  text-decoration: none;
  margin-right: 16px;
}

/* Navigation tab hover effects */
.pages-tabs:hover {
  cursor: pointer;
  font-weight: normal;
  text-decoration: none;
}

/* Active navigation tab styling */
.pages-tabs:active,
.pages-tabs.active,
.pages-tabs[aria-current="page"] {
  font-weight: normal;
  text-decoration: none;
}

/* ========================================
   HERO SECTION STYLES
   ======================================== */

/* Main hero section with background image */
.hero-section {
  background-image: url('Images/home.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
}

/* Hero main title */
.hero-section p:first-child {
  font-size: 78px;
  font-weight: normal;
  font-family: sans-serif;
  margin-bottom: 30px;
  color: #333;
}

/* Hero subtitle/description */
.hero-section p:nth-child(2) {
  font-size: 26px;
  font-family: sans-serif;
  font-weight: normal;
  margin-bottom: 30px;
  color: #333;
}

/* Hero call-to-action button */
.hero-section button {
  font-size: 16px;
  font-family: sans-serif;
  font-weight: normal;
  padding: 20px 30px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  background-color: black;
  color: white;
  margin-top: 50px;
}

/* ========================================
   CATEGORIES & PRODUCTS GRID SECTIONS
   ======================================== */

/* Container styling for categories and products sections */
.categories-section,
.products-section {
  padding: 40px 20px;
  text-align: center;
}

/* Section titles for categories and products */
.categories-section p,
.products-section p {
  font-size: 32px;
  font-weight: lighter;
  margin-bottom: 30px;
  text-align: center;
  font-family: sans-serif;
  color: #333;
}

/* Grid layout for categories and products (2x2 layout) */
.categories-grid,
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

/* Figure elements within grids */
.categories-grid figure,
.products-grid figure {
  text-align: center;
  margin-bottom: 30px;
}

/* Grid images with hover animations */
.categories-grid img,
.products-grid img {
  width: 100%;
  max-width: 300px;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

/* Grid image hover effects - scale and shadow */
.categories-grid img:hover,
.products-grid img:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}



/* Grid image captions (product names) */
.categories-grid figcaption,
.products-grid figcaption {
  font-size: 20px;
  font-weight: lighter;
  font-family: sans-serif;
  color: #333;
}

/* Category title text (first paragraph) */
.categories-grid figure p:first-of-type {
  font-size: 18px;
  font-weight: normal;
  font-family: sans-serif;
  text-align: left;
  margin: 0 0 5px 0;
  color: #333;
}

/* Category description text (middle paragraph) */
.categories-grid figure p:nth-of-type(2) {
  font-size: 14px;
  font-weight: lighter;
  font-family: sans-serif;
  text-align: left;
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.4;
}

/* Category shop link text (last paragraph) */
.categories-grid figure p:last-of-type {
  font-size: 14px;
  font-weight: normal;
  font-family: sans-serif;
  text-align: left;
  margin: 0 0 0 0;
  color: #2c2c2c;
  transition: color 0.3s ease;
}

/* Category clickable link styling */
.category-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: transform 0.2s ease, box-shadow 0.3s ease;
  padding: 15px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Category link hover effects */
.category-link:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Category link hover effect for shop text */
.category-link:hover p:last-of-type {
  color: #000;
  transform: translateX(5px);
}

/* Ensure category link text maintains styling */
.category-link p {
  color: inherit;
  transition: transform 0.3s ease;
}

.category-link p:first-of-type {
  color: #333;
}

.category-link p:nth-of-type(2) {
  color: #666;
}

/* ========================================
   ABOUT PAGE STYLES
   ======================================== */

/* About page content sections */
.about-section {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

/* About page section titles */
.about-title {
  font-size: 28px;
  font-weight: normal;
  font-family: sans-serif;
  margin-bottom: 20px;
  color: black;
  text-align: left;
}

/* About page paragraph text */
.about-text {
  font-size: 18px;
  font-weight: lighter;
  font-family: sans-serif;
  line-height: 1.6;
  color: #333;
  margin-bottom: 30px;
  text-align: left;
}

/* ========================================
   GLOBAL STYLES
   ======================================== */

/* Navigation bar styling */
nav {
  padding: 20px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #ddd;
}

/* Global body reset and font */
body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}

/* Global button styling */
button {
  font-size: 16px;
  font-family: sans-serif;
  font-weight: normal;
  padding: 12px 24px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  background-color: black;
  color: white;
  transition: background-color 0.3s ease;
}

/* Global button hover effects */
button:hover {
  background-color: #333;
}

/* ========================================
   FOOTER STYLES
   ======================================== */

/* Main footer container */
.footer {
  background-color: #2c2c2c;
  color: white;
  padding: 40px 20px 20px;
  margin-top: 60px;
}

/* Footer content grid layout */
.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

/* Individual footer columns */
.footer-column {
  text-align: left;
}

/* Footer column headings */
.footer-column h4 {
  font-size: 18px;
  font-weight: normal;
  font-family: sans-serif;
  margin-bottom: 15px;
  color: white;
}

/* Footer column paragraph text */
.footer-column p {
  font-size: 14px;
  font-weight: lighter;
  font-family: sans-serif;
  line-height: 1.5;
  margin-bottom: 10px;
  color: #ccc;
}

/* Footer column links */
.footer-column a {
  color: #ccc;
  text-decoration: none;
  font-size: 14px;
  font-family: sans-serif;
  display: block;
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

/* Footer link hover effects */
.footer-column a:hover {
  color: white;
}

/* Social media icons container */
.footer-social {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* Social media link styling */
.footer-social a {
  display: inline-block;
  margin-bottom: 0;
}

/* Social media icons */
.footer-social img {
  width: 30px;
  height: 30px;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Social media icon hover effects */
.footer-social img:hover {
  opacity: 0.7;
  transform: scale(1.1);
}

/* Footer copyright notice */
.footer-copyright {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #444;
  font-size: 14px;
  font-family: sans-serif;
  color: #ccc;
}

/* Footer responsive design */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

/* ========================================
   MISSION, VISION, VALUES TABLE
   ======================================== */

/* Main table container for Mission, Vision, Values */
.mvv-table {
  width: 100%;
  max-width: 1000px;
  margin: 40px auto;
  border-collapse: collapse;
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Table header cells */
.mvv-table th {
  background-color: #2c2c2c;
  color: white;
  font-size: 20px;
  font-weight: normal;
  font-family: sans-serif;
  padding: 20px 15px;
  text-align: center;
  border: none;
}

/* Table data cells */
.mvv-table td {
  font-size: 16px;
  font-weight: lighter;
  font-family: sans-serif;
  line-height: 1.6;
  padding: 25px 20px;
  text-align: center;
  color: #333;
  border: none;
  vertical-align: top;
}

/* Alternating row colors */
.mvv-table tr:nth-child(even) {
  background-color: #ffffff;
}

/* Table responsive design */
@media (max-width: 768px) {
  .mvv-table th,
  .mvv-table td {
    font-size: 14px;
    padding: 15px 10px;
  }

  .mvv-table th {
    font-size: 16px;
  }
}

/* ========================================
   IMAGE HOVER EFFECTS
   ======================================== */

/* General image hover effects (excludes specific grid and footer images) */
img:not(.footer-social img):not(.categories-grid img):not(.products-grid img):not(.modal-image) {
  transition: transform 0.3s ease, filter 0.3s ease;
}

/* General image hover animation (excludes modal images) */
img:not(.footer-social img):not(.categories-grid img):not(.products-grid img):not(.modal-image):hover {
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* ========================================
   CONTACT FORM STYLES
   ======================================== */

/* Contact form section container */
.contact-form-section {
  padding: 40px 20px;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact form layout */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form field groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Form field labels */
.form-group label {
  font-size: 16px;
  font-weight: normal;
  font-family: sans-serif;
  color: #333;
  text-align: left;
}

/* Form input fields and textarea */
.form-group input,
.form-group textarea {
  padding: 12px 15px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  font-family: sans-serif;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

/* Form field focus effects */
.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2c2c2c;
  background-color: white;
}

/* Textarea specific styling */
.form-group textarea {
  min-height: 120px;
  resize: vertical;
}

/* Form submit button */
.submit-button {
  width: 100%;
  padding: 15px 20px;
  background-color: #2c2c2c;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 18px;
  font-weight: normal;
  font-family: sans-serif;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  margin-top: 10px;
}

/* Submit button hover effects */
.submit-button:hover {
  background-color: #1a1a1a;
  transform: translateY(-2px);
}

/* ========================================
   CONTACT INFORMATION SECTION
   ======================================== */

/* Contact information section container */
.contact-info-section {
  padding: 40px 20px;
  max-width: 600px;
  margin: 0 auto;
  text-align: left;
}

/* Contact information main title */
.contact-info-section .about-title {
  font-size: 28px;
  font-weight: normal;
  font-family: sans-serif;
  margin-bottom: 30px;
  color: #333;
  text-align: left;
}

/* Contact information section headers (Address, Phone, etc.) */
.contact-info-section h4 {
  font-size: 18px;
  font-weight: normal;
  font-family: sans-serif;
  color: #2c2c2c;
  margin: 25px 0 10px 0;
  text-align: left;
  border-bottom: 2px solid #2c2c2c;
  padding-bottom: 5px;
  display: inline-block;
}

/* Contact information paragraph text */
.contact-info-section p {
  font-size: 16px;
  font-weight: lighter;
  font-family: sans-serif;
  color: #666;
  margin: 5px 0;
  text-align: left;
  line-height: 1.5;
}

/* Ensure contact info paragraphs have no extra indentation */
.contact-info-section p:not(.about-title) {
  margin-left: 0;
  padding-left: 0;
}

/* ========================================
   SECTION HEADER WITH BACK LINK
   ======================================== */

/* Section header container for back link and title */
.section-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 20px;
  position: relative;
}

/* Back link styling */
.back-link {
  font-size: 16px;
  font-weight: normal;
  font-family: sans-serif;
  color: #2c2c2c;
  text-decoration: none;
  transition: color 0.3s ease, transform 0.2s ease;
  position: absolute;
  left: 20px;
}

/* Back link hover effects */
.back-link:hover {
  color: #000;
  transform: translateX(-3px);
}

/* Section title when used with back link */
.section-header .section-title {
  font-size: 32px;
  font-weight: lighter;
  font-family: sans-serif;
  margin: 0;
  color: #333;
}

/* Responsive design for section header */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .section-header .section-title {
    font-size: 28px;
  }
}

/* ========================================
   PRODUCT LINK STYLES
   ======================================== */

/* Product "View Product ->" link styling */
.product-link {
  display: inline-block;
  font-size: 14px;
  font-weight: normal;
  font-family: sans-serif;
  color: #2c2c2c;
  text-decoration: none;
  margin-top: 10px;
  padding: 8px 15px;
  border: 2px solid #2c2c2c;
  border-radius: 5px;
  background-color: transparent;
  transition: all 0.3s ease;
  text-align: center;
}

/* Product link hover effects */
.product-link:hover {
  background-color: #2c2c2c;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(44, 44, 44, 0.2);
}

/* Ensure product figures have proper spacing */
.products-grid figure {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Product figure hover effects */
.products-grid figure:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Make product images clickable */
.products-grid img {
  cursor: pointer;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.products-grid img:hover {
  transform: scale(1.02);
  opacity: 0.9;
}

/* ========================================
   CSS-ONLY IMAGE MODAL STYLES
   ======================================== */

/* CSS-only modal overlay background */
.css-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

/* Show modal when targeted */
.css-modal:target {
  display: flex;
}

/* CSS modal content container */
.css-modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s ease;
}

/* CSS modal image */
.css-modal-image {
  width: 100%;
  height: auto;
  max-width: 600px;
  max-height: 600px;
  object-fit: contain;
  border-radius: 5px;
}

/* Ensure CSS modal image has no hover effects */
.css-modal-image:hover {
  transform: none !important;
  filter: none !important;
  opacity: 1 !important;
}

/* CSS modal close button */
.css-modal-close {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #2c2c2c;
  color: white;
  text-decoration: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

/* CSS close button hover effect */
.css-modal-close:hover {
  background-color: #1a1a1a;
}

/* Modal fade-in animation */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================
   PRODUCT PAGE LAYOUT
   ======================================== */

/* Product page container */
.product-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Two-column product layout */
.product-container {
  display: grid;
  grid-template-columns: 60% 40%;
  gap: 40px;
  margin-top: 30px;
}

/* Left Column - Product Images */
.product-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Main product image */
.main-image {
  width: 100%;
  aspect-ratio: 1;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

/* Thumbnail images container */
.thumbnail-images {
  display: flex;
  gap: 0;
  width: 100%;
}

/* Individual thumbnail styling */
.thumbnail {
  flex: 1;
  aspect-ratio: 1;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  margin-right: 10px;
}

.thumbnail:last-child {
  margin-right: 0;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail.active {
  border-color: #2c2c2c;
}

/* Right Column - Product Details */
.product-details {
  padding: 20px 0;
}

/* Product title */
.product-title {
  font-size: 32px;
  font-weight: normal;
  font-family: sans-serif;
  color: #333;
  margin: 0 0 15px 0;
  line-height: 1.2;
}

/* Product price */
.product-price {
  font-size: 28px;
  font-weight: bold;
  font-family: sans-serif;
  color: #2c2c2c;
  margin: 0 0 25px 0;
}

/* Product description */
.product-description {
  margin-bottom: 25px;
}

.product-description p {
  font-size: 16px;
  font-family: sans-serif;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Product features */
.product-features {
  margin-bottom: 30px;
}

.product-features h3 {
  font-size: 18px;
  font-weight: normal;
  font-family: sans-serif;
  color: #333;
  margin: 0 0 15px 0;
}

.product-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product-features li {
  font-size: 14px;
  font-family: sans-serif;
  color: #666;
  padding: 5px 0;
  position: relative;
  padding-left: 20px;
}

.product-features li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #2c2c2c;
  font-weight: bold;
}

/* Product actions section */
.product-actions {
  margin-bottom: 30px;
}

/* Quantity section */
.quantity-section {
  margin-bottom: 20px;
}

.quantity-section label {
  font-size: 16px;
  font-family: sans-serif;
  color: #333;
  margin-right: 10px;
}

.quantity-section select {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  font-family: sans-serif;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.quantity-section select:focus {
  outline: none;
  border-color: #2c2c2c;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Add to Cart button */
.add-to-cart-btn {
  background-color: #2c2c2c;
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: normal;
  font-family: sans-serif;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.add-to-cart-btn:hover {
  background-color: #1a1a1a;
  transform: translateY(-2px);
}

/* Add to Wishlist button */
.add-to-wishlist-btn {
  background-color: white;
  color: #2c2c2c;
  border: 2px solid #2c2c2c;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: normal;
  font-family: sans-serif;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease;
}

.add-to-wishlist-btn:hover {
  background-color: #2c2c2c;
  color: white;
  transform: translateY(-2px);
}

/* Product footer information */
.product-footer {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.product-footer p {
  font-size: 14px;
  font-family: sans-serif;
  color: #666;
  margin: 8px 0;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .product-title {
    font-size: 28px;
  }

  .product-price {
    font-size: 24px;
  }

  .thumbnail-images {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .product-page {
    padding: 15px;
  }

  .product-container {
    gap: 20px;
  }

  .product-title {
    font-size: 24px;
  }

  .product-price {
    font-size: 20px;
  }

  .thumbnail-images {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .add-to-cart-btn,
  .add-to-wishlist-btn {
    padding: 12px 20px;
    font-size: 14px;
  }
}

