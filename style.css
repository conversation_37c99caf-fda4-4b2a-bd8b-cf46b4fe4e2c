.logo {
  color: black;
  font-size: 24px;
  font-weight: normal;
  font-family: sans-serif;
  margin-right: 60px;
}

.pages-tabs {
  color: black;
  font-size: 16px;
  font-family: sans-serif;
  font-weight: lighter;
  text-decoration: none;
  margin-right: 16px;
}

.pages-tabs:hover {
  cursor: pointer;
  font-weight: normal;
  text-decoration: none;
}

.pages-tabs:active,
.pages-tabs.active,
.pages-tabs[aria-current="page"] {
  font-weight: normal;
  text-decoration: none;
}

.hero-section {
  background-image: url('Images/home.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
}

.hero-section p:first-child {
  font-size: 78px;
  font-weight: normal;
  font-family: sans-serif;
  margin-bottom: 30px;
}

.hero-section p:nth-child(2) {
  font-size: 26px;
  font-family: sans-serif;
  font-weight: normal;
  margin-bottom: 30px;
}

.hero-section button {
  font-size: 16px;
  font-family: sans-serif;
  font-weight: normal;
  padding: 20px 30px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  background-color: black;
  color: white;
}

.categories-section,
.products-section {
  padding: 40px 20px;
  text-align: center;
}

.categories-section p,
.products-section p {
  font-size: 32px;
  font-weight: lighter;
  margin-bottom: 30px;
  text-align: center;
  font-family: sans-serif;
}

.categories-grid,
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.categories-grid figure,
.products-grid figure {
  text-align: center;
}

.categories-grid img,
.products-grid img {
  width: 100%;
  max-width: 300px;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
}

.categories-grid figcaption,
.products-grid figcaption {
  margin-top: 10px;
  font-size: 20px;
  font-weight: lighter;
  font-family: sans-serif;
}

/* About Page Styles */
.about-section {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-title {
  font-size: 28px;
  font-weight: normal;
  font-family: sans-serif;
  margin-bottom: 20px;
  color: black;
  text-align: left;
}

.about-text {
  font-size: 18px;
  font-weight: lighter;
  font-family: sans-serif;
  line-height: 1.6;
  color: #333;
  margin-bottom: 30px;
  text-align: left;
}

/* Navigation consistency fix */
nav {
  padding: 20px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #ddd;
}

/* General body styling */
body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}

/* Button styling for consistency */
button {
  font-size: 16px;
  font-family: sans-serif;
  font-weight: normal;
  padding: 12px 24px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  background-color: black;
  color: white;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #333;
}

/* Footer Styles */
.footer {
  background-color: #2c2c2c;
  color: white;
  padding: 40px 20px 20px;
  margin-top: 60px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.footer-column {
  text-align: left;
}

.footer-column h4 {
  font-size: 18px;
  font-weight: normal;
  font-family: sans-serif;
  margin-bottom: 15px;
  color: white;
}

.footer-column p {
  font-size: 14px;
  font-weight: lighter;
  font-family: sans-serif;
  line-height: 1.5;
  margin-bottom: 10px;
  color: #ccc;
}

.footer-column a {
  color: #ccc;
  text-decoration: none;
  font-size: 14px;
  font-family: sans-serif;
  display: block;
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.footer-column a:hover {
  color: white;
}

.footer-social {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.footer-social a {
  display: inline-block;
  margin-bottom: 0;
}

.footer-social img {
  width: 30px;
  height: 30px;
  transition: opacity 0.3s ease;
}

.footer-social img:hover {
  opacity: 0.7;
}

/* Responsive footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

