# CasaMia Furniture Website

## 🏠 Project Overview

CasaMia is a modern, responsive furniture e-commerce website built with **pure HTML5 and CSS3** - no JavaScript dependencies. The website showcases premium furniture collections with a clean, professional design and optimal user experience.

## 🛠️ Technology Stack

- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern styling with Grid, Flexbox, and CSS animations
- **Pure CSS Modals**: Using `:target` pseudo-selector (no JavaScript)
- **Responsive Design**: Mobile-first approach with media queries
- **No Dependencies**: Zero external libraries or frameworks

## 📁 Project Structure

```
CasaMia_v01/
├── README.md                          # This documentation file
├── style.css                          # Single CSS file for entire website
├── home.html                          # Homepage with hero section and featured items
├── categories.html                    # Category listing page with clickable cards
├── shop.html                          # Shop page (placeholder)
├── about.html                         # About page with company information
├── contact.html                       # Contact form and business information
├── Images/                            # All website images organized by category
│   ├── fb.jpg, instagram.jpg, x.jpg   # Social media icons
│   ├── home.jpg                       # Hero section background
│   ├── livingRoom/                    # Living room product images
│   ├── storageUnit/                   # Storage unit product images
│   ├── outdoorFurniture/              # Outdoor furniture images
│   └── lighting/                      # Lighting product images
├── livingRoomProducts/
│   ├── index.html                     # Living room product listing
│   └── product01.html                 # Individual product page
├── storageUnitProducts/
│   └── index.html                     # Storage unit product listing
├── outdoorFurnitureProducts/
│   └── index.html                     # Outdoor furniture product listing
└── lightingProducts/
    └── index.html                     # Lighting product listing
```

## 🎨 Design System

### Color Palette
- **Primary Brand Color**: `#2c2c2c` (Dark gray)
- **Text Colors**: `#333` (headings), `#666` (body text), `#ccc` (light text)
- **Background Colors**: `#f9f9f9` (light sections), `white` (cards)
- **Accent Colors**: `#1a1a1a` (hover states)

### Typography
- **Font Family**: `sans-serif` (system fonts for performance)
- **Heading Sizes**: 32px (main), 28px (sections), 18px (subsections)
- **Body Text**: 16px (standard), 14px (small text)
- **Font Weights**: `normal` (default), `lighter` (descriptions), `bold` (prices)

### Layout System
- **Grid Layout**: CSS Grid for 2x2 category displays
- **Flexbox**: Navigation, footer columns, form layouts
- **Responsive Breakpoints**: 768px (tablet), 480px (mobile)
- **Container Max-Width**: 1200px (large sections), 800px (content), 600px (forms)

## 🔧 Key Features

### 1. Pure CSS Image Modals
- **Technology**: `:target` pseudo-selector
- **No JavaScript**: Fully functional with CSS only
- **Implementation**: Each image links to `#modal1`, `#modal2`, etc.
- **Close Function**: Links to `#` to remove target

### 2. Responsive Navigation
- **Active States**: `.active` class shows current page
- **Hover Effects**: Smooth font-weight transitions
- **Mobile Ready**: Stacks appropriately on small screens

### 3. Category Cards
- **Fully Clickable**: Entire card area is interactive
- **Three-Level Text**: Title, description, action text
- **Hover Effects**: Card lift and text animations
- **Semantic HTML**: Proper `figure` and `figcaption` elements

### 4. Contact Form
- **Pure HTML**: Standard form with validation attributes
- **Styled Inputs**: Custom CSS styling with focus states
- **Responsive**: Stacks vertically on mobile devices
- **Backend Ready**: Form action points to `submit_form.php`

## 📱 Responsive Design

### Desktop (1200px+)
- Two-column product layout (60% images, 40% details)
- 4-column footer
- 2x2 category grid

### Tablet (768px - 1199px)
- Single-column product layout
- 2-column footer
- Maintained category grid

### Mobile (< 768px)
- Stacked navigation
- Single-column footer
- 2-column category grid (reduced)
- Optimized touch targets

## 🚀 Getting Started

### Prerequisites
- Web server (Apache, Nginx, or simple HTTP server)
- Modern web browser with CSS3 support

### Installation
1. Clone or download the project files
2. Place files in web server directory
3. Ensure proper file permissions for images
4. Open `home.html` in web browser

### Development Setup
1. Use any text editor or IDE
2. Live server extension recommended for development
3. Browser developer tools for responsive testing
4. No build process required - direct file editing

## 🔄 Adding New Content

### Adding New Products
1. **Product Images**: Add to appropriate `/Images/[category]/` folder
2. **Product Listing**: Add `<figure>` element to product grid
3. **Product Page**: Copy `product01.html` and modify content
4. **Update Links**: Ensure navigation links are correct

### Adding New Categories
1. **Create Folder**: `[categoryName]Products/`
2. **Create Index**: Copy existing category index.html
3. **Add Images**: Create `/Images/[categoryName]/` folder
4. **Update Categories Page**: Add new category card
5. **Update Navigation**: Add links where appropriate

### Modifying Styles
- **Single CSS File**: All styles in `style.css`
- **Organized Sections**: Clearly commented style blocks
- **CSS Variables**: Consider adding for easier theme changes
- **Mobile First**: Always test responsive behavior

## 🎯 Best Practices

### HTML
- Use semantic elements (`nav`, `main`, `section`, `article`, `figure`)
- Include proper `alt` attributes for all images
- Maintain consistent class naming conventions
- Validate HTML5 compliance

### CSS
- Follow mobile-first responsive design
- Use CSS Grid and Flexbox for layouts
- Maintain consistent spacing and typography
- Comment complex selectors and layout sections

### Performance
- Optimize images for web (WebP format recommended)
- Minimize CSS file size
- Use system fonts for faster loading
- Implement proper caching headers

### Accessibility
- Maintain proper heading hierarchy (h1, h2, h3, etc.)
- Ensure sufficient color contrast ratios
- Include focus states for keyboard navigation
- Test with screen readers

## 🐛 Common Issues & Solutions

### Modal Not Working
- Check that modal IDs match link targets
- Ensure `:target` CSS is properly implemented
- Verify no JavaScript conflicts

### Responsive Layout Issues
- Test at various breakpoints
- Check CSS Grid and Flexbox browser support
- Validate media query syntax

### Image Loading Problems
- Verify file paths are correct
- Check image file permissions
- Ensure proper image formats

## 📞 Support & Maintenance

### File Organization
- Keep images organized in category folders
- Maintain consistent naming conventions
- Regular backup of all files

### Updates & Changes
- Test changes across all browsers
- Validate HTML and CSS after modifications
- Update documentation when adding features

### Performance Monitoring
- Monitor page load times
- Optimize images regularly
- Check for broken links periodically

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Maintainer**: Development Team
